import 'package:flutter/material.dart';

class CarType {
  final String id;
  final String name;
  final String description;
  final double pricePerKm;
  final String currency;
  final String imageAsset;
  final Color color;
  final int capacity;
  final String estimatedTime;

  CarType({
    required this.id,
    required this.name,
    required this.description,
    required this.pricePerKm,
    required this.currency,
    required this.imageAsset,
    required this.color,
    required this.capacity,
    required this.estimatedTime,
  });

  factory CarType.fromJson(Map<String, dynamic> json) {
    return CarType(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      pricePerKm: json['pricePerKm'].toDouble(),
      currency: json['currency'],
      imageAsset: json['imageAsset'],
      color: Color(json['color']),
      capacity: json['capacity'],
      estimatedTime: json['estimatedTime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'pricePerKm': pricePerKm,
      'currency': currency,
      'imageAsset': imageAsset,
      'color': color.value,
      'capacity': capacity,
      'estimatedTime': estimatedTime,
    };
  }
}
