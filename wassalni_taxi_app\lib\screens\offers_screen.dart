import 'package:flutter/material.dart';
import '../models/offer.dart';
import '../utils/colors.dart';
import '../utils/constants.dart';

class OffersScreen extends StatefulWidget {
  const OffersScreen({Key? key}) : super(key: key);

  @override
  State<OffersScreen> createState() => _OffersScreenState();
}

class _OffersScreenState extends State<OffersScreen> {
  // Sample offers data
  final List<Offer> offers = [
    Offer(
      id: '1',
      title: 'خصم 20%',
      description: 'احصل على خصم 20% على أول 3 رحلات',
      imageUrl: 'https://via.placeholder.com/300x150/00BFA5/FFFFFF?text=20%25+OFF',
      discount: 20,
      discountType: 'percentage',
      validUntil: DateTime.now().add(const Duration(days: 30)),
      isActive: true,
      promoCode: 'FIRST20',
    ),
    Offer(
      id: '2',
      title: 'رحلة مجانية',
      description: 'احجز 5 رحلات واحصل على السادسة مجاناً',
      imageUrl: 'https://via.placeholder.com/300x150/4CAF50/FFFFFF?text=FREE+RIDE',
      discount: 100,
      discountType: 'percentage',
      validUntil: DateTime.now().add(const Duration(days: 15)),
      isActive: true,
      promoCode: 'FREE6TH',
    ),
    Offer(
      id: '3',
      title: 'خصم 15 ريال',
      description: 'خصم ثابت 15 ريال على الرحلات الفاخرة',
      imageUrl: 'https://via.placeholder.com/300x150/9C27B0/FFFFFF?text=15+SAR+OFF',
      discount: 15,
      discountType: 'fixed',
      validUntil: DateTime.now().add(const Duration(days: 7)),
      isActive: true,
      promoCode: 'LUXURY15',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'العروض المتاحة',
          style: TextStyle(
            color: AppColors.primaryText,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: offers.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(AppConstants.mediumPadding),
              itemCount: offers.length,
              itemBuilder: (context, index) {
                return _buildOfferCard(offers[index]);
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_offer_outlined,
            size: 64,
            color: AppColors.hintText,
          ),
          const SizedBox(height: AppConstants.mediumPadding),
          const Text(
            'لا توجد عروض متاحة حالياً',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOfferCard(Offer offer) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.mediumPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Offer image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppConstants.borderRadius),
              topRight: Radius.circular(AppConstants.borderRadius),
            ),
            child: Container(
              height: 150,
              width: double.infinity,
              color: AppColors.primaryGreen.withOpacity(0.1),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.local_offer,
                      size: 60,
                      color: AppColors.primaryGreen,
                    ),
                  ),
                  if (offer.isActive)
                    Positioned(
                      top: AppConstants.smallPadding,
                      right: AppConstants.smallPadding,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.successColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'نشط',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          
          // Offer content
          Padding(
            padding: const EdgeInsets.all(AppConstants.mediumPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and discount
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        offer.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryText,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primaryGreen.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        offer.discountType == 'percentage'
                            ? '${offer.discount.toInt()}%'
                            : '${offer.discount.toInt()} ريال',
                        style: const TextStyle(
                          color: AppColors.primaryGreen,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.smallPadding),
                
                // Description
                Text(
                  offer.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.secondaryText,
                    height: 1.4,
                  ),
                ),
                
                const SizedBox(height: AppConstants.mediumPadding),
                
                // Promo code and validity
                Row(
                  children: [
                    if (offer.promoCode != null) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.backgroundColor,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: AppColors.borderColor,
                            style: BorderStyle.solid,
                          ),
                        ),
                        child: Text(
                          offer.promoCode!,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: AppColors.primaryText,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                    ],
                    Expanded(
                      child: Text(
                        'صالح حتى ${_formatDate(offer.validUntil)}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.hintText,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.mediumPadding),
                
                // Action buttons
                Row(
                  children: [
                    if (offer.promoCode != null)
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            _copyPromoCode(offer.promoCode!);
                          },
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: AppColors.primaryGreen),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                AppConstants.borderRadius,
                              ),
                            ),
                          ),
                          child: const Text(
                            'نسخ الكود',
                            style: TextStyle(color: AppColors.primaryGreen),
                          ),
                        ),
                      ),
                    if (offer.promoCode != null)
                      const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: offer.isActive
                            ? () {
                                _useOffer(offer);
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryGreen,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              AppConstants.borderRadius,
                            ),
                          ),
                        ),
                        child: const Text(
                          'استخدم العرض',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _copyPromoCode(String promoCode) {
    // Copy to clipboard functionality would go here
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ الكود: $promoCode'),
        backgroundColor: AppColors.successColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _useOffer(Offer offer) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('استخدام العرض'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('هل تريد استخدام عرض "${offer.title}"؟'),
              if (offer.promoCode != null) ...[
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'كود الخصم: ${offer.promoCode}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryGreen,
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to booking with applied offer
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تطبيق العرض! يمكنك الآن الحجز.'),
                    backgroundColor: AppColors.successColor,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
              ),
              child: const Text('احجز الآن', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}
