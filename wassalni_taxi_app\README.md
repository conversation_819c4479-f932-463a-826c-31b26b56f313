# تطبيق وصلني لحجز التاكسي

تطبيق Flutter لحجز التاكسي مع واجهة مستخدم عربية حديثة وميزات شاملة.

## الميزات الرئيسية

### 🏠 الشاشة الرئيسية
- خريطة تفاعلية لعرض الموقع الحالي
- شريط البحث لإدخال الوجهة
- اختيار نوع السيارة (اقتصادي، مريح، فاخر)
- عرض الأسعار والأوقات المتوقعة
- زر الحجز المباشر

### 🚗 إدارة الرحلات
- عرض الرحلات الحالية
- تاريخ الرحلات المكتملة
- الرحلات الملغية
- تفاصيل كاملة لكل رحلة
- تقييم السائقين
- إمكانية إعادة الحجز

### 🎁 العروض والخصومات
- عرض العروض المتاحة
- أكواد الخصم
- تواريخ انتهاء العروض
- تطبيق العروض على الحجوزات

### 👤 الملف الشخصي
- معلومات المستخدم
- إحصائيات الرحلات والتقييمات
- إعدادات الحساب
- طرق الدفع
- العناوين المحفوظة
- إعدادات الإشعارات
- اختيار اللغة
- المساعدة والدعم

## التقنيات المستخدمة

- **Flutter**: إطار العمل الرئيسي
- **Dart**: لغة البرمجة
- **Google Maps**: للخرائط والموقع
- **Provider**: لإدارة الحالة
- **HTTP**: للتواصل مع الخادم
- **Geolocator**: لتحديد الموقع
- **Permission Handler**: لإدارة الأذونات

## بنية المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── car_type.dart
│   ├── trip.dart
│   └── offer.dart
├── screens/                  # الشاشات الرئيسية
│   ├── home_screen.dart
│   ├── trips_screen.dart
│   ├── offers_screen.dart
│   └── profile_screen.dart
├── widgets/                  # الويدجت المخصصة
│   ├── car_type_card.dart
│   └── trip_card.dart
└── utils/                    # الأدوات المساعدة
    ├── colors.dart
    └── constants.dart
```

## التشغيل

### المتطلبات
- Flutter SDK (الإصدار 3.7.2 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- جهاز Android/iOS أو محاكي

### خطوات التشغيل

1. **استنساخ المشروع**
   ```bash
   git clone [repository-url]
   cd wassalni_taxi_app
   ```

2. **تثبيت التبعيات**
   ```bash
   flutter pub get
   ```

3. **تشغيل التطبيق**
   ```bash
   # للأندرويد
   flutter run

   # للويب
   flutter run -d chrome

   # لنظام Windows
   flutter run -d windows
   ```

## الاختبار

```bash
# تشغيل الاختبارات
flutter test

# تحليل الكود
flutter analyze
```

## الميزات المستقبلية

- [ ] تكامل مع خرائط Google الحقيقية
- [ ] نظام الدفع الإلكتروني
- [ ] الإشعارات الفورية
- [ ] تتبع الرحلة في الوقت الفعلي
- [ ] دردشة مع السائق
- [ ] تقييم الرحلات
- [ ] العناوين المفضلة
- [ ] الرحلات المجدولة
- [ ] نظام النقاط والمكافآت

---

تم تطوير هذا التطبيق بـ ❤️ باستخدام Flutter
