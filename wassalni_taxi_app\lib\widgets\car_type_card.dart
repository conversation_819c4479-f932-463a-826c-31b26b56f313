import 'package:flutter/material.dart';
import '../models/car_type.dart';
import '../utils/colors.dart';
import '../utils/constants.dart';

class CarTypeCard extends StatelessWidget {
  final CarType carType;
  final bool isSelected;
  final VoidCallback onTap;

  const CarTypeCard({
    Key? key,
    required this.carType,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: AppConstants.smallPadding,
          vertical: AppConstants.smallPadding / 2,
        ),
        padding: const EdgeInsets.all(AppConstants.mediumPadding),
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: isSelected 
                ? AppColors.selectedBorderColor 
                : AppColors.borderColor,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Car image placeholder
            Container(
              width: 60,
              height: 40,
              decoration: BoxDecoration(
                color: carType.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.directions_car,
                color: carType.color,
                size: 30,
              ),
            ),
            const SizedBox(width: AppConstants.mediumPadding),
            
            // Car details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    carType.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryText,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    carType.description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.secondaryText,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    carType.estimatedTime,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.hintText,
                    ),
                  ),
                ],
              ),
            ),
            
            // Price
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${carType.pricePerKm.toStringAsFixed(1)} ${carType.currency}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
                const Text(
                  'للكيلومتر',
                  style: TextStyle(
                    fontSize: 10,
                    color: AppColors.hintText,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
