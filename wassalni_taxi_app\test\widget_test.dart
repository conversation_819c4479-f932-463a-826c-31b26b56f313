// This is a basic Flutter widget test for Wassalni Taxi App.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:wassalni_taxi_app/main.dart';

void main() {
  testWidgets('App loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const WassalniApp());

    // Verify that the app loads with the main screen
    expect(find.text('وصلني'), findsOneWidget);
    expect(find.text('الرئيسية'), findsOneWidget);
    expect(find.text('رحلاتي'), findsOneWidget);
    expect(find.text('العروض'), findsOneWidget);
    expect(find.text('الملف الشخصي'), findsOneWidget);
  });

  testWidgets('Bottom navigation works', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const Was<PERSON><PERSON><PERSON><PERSON>());

    // Tap on trips tab
    await tester.tap(find.text('رحلاتي'));
    await tester.pump();

    // Verify that trips screen is shown
    expect(find.text('رحلاتي'), findsWidgets);
  });
}
