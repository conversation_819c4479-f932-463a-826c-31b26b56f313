import 'package:flutter/material.dart';

class AppColors {
  // Primary colors
  static const Color primaryGreen = Color(0xFF00BFA5);
  static const Color lightGreen = Color(0xFF4DD0E1);
  static const Color darkGreen = Color(0xFF00897B);
  
  // Background colors
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardBackground = Colors.white;
  
  // Text colors
  static const Color primaryText = Color(0xFF212121);
  static const Color secondaryText = Color(0xFF757575);
  static const Color hintText = Color(0xFF9E9E9E);
  
  // Status colors
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFF44336);
  
  // Car type colors
  static const Color economyColor = Color(0xFF2196F3);
  static const Color comfortColor = Color(0xFF9C27B0);
  static const Color luxuryColor = Color(0xFF795548);
  
  // Border colors
  static const Color borderColor = Color(0xFFE0E0E0);
  static const Color selectedBorderColor = primaryGreen;
}
