class Trip {
  final String id;
  final String from;
  final String to;
  final String status;
  final DateTime dateTime;
  final double price;
  final String currency;
  final String carType;
  final String driverName;
  final String driverImage;
  final double rating;
  final String? notes;

  Trip({
    required this.id,
    required this.from,
    required this.to,
    required this.status,
    required this.dateTime,
    required this.price,
    required this.currency,
    required this.carType,
    required this.driverName,
    required this.driverImage,
    required this.rating,
    this.notes,
  });

  factory Trip.fromJson(Map<String, dynamic> json) {
    return Trip(
      id: json['id'],
      from: json['from'],
      to: json['to'],
      status: json['status'],
      dateTime: DateTime.parse(json['dateTime']),
      price: json['price'].toDouble(),
      currency: json['currency'],
      carType: json['carType'],
      driverName: json['driverName'],
      driverImage: json['driverImage'],
      rating: json['rating'].toDouble(),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'from': from,
      'to': to,
      'status': status,
      'dateTime': dateTime.toIso8601String(),
      'price': price,
      'currency': currency,
      'carType': carType,
      'driverName': driverName,
      'driverImage': driverImage,
      'rating': rating,
      'notes': notes,
    };
  }
}
