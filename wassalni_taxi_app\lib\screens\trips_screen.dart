import 'package:flutter/material.dart';
import '../models/trip.dart';
import '../widgets/trip_card.dart';
import '../utils/colors.dart';
import '../utils/constants.dart';

class TripsScreen extends StatefulWidget {
  const TripsScreen({Key? key}) : super(key: key);

  @override
  State<TripsScreen> createState() => _TripsScreenState();
}

class _TripsScreenState extends State<TripsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Sample trips data
  final List<Trip> activeTrips = [
    Trip(
      id: '1',
      from: 'الرياض - حي النخيل',
      to: 'الرياض - مطار الملك خالد',
      status: AppConstants.tripStatusActive,
      dateTime: DateTime.now(),
      price: 45.0,
      currency: 'ريال',
      carType: 'مريح',
      driverName: 'أحمد محمود',
      driverImage: 'assets/images/driver1.jpg',
      rating: 4.8,
    ),
  ];

  final List<Trip> completedTrips = [
    Trip(
      id: '2',
      from: 'الرياض - مول الرياض غاليري',
      to: 'الرياض - حي الملز',
      status: AppConstants.tripStatusCompleted,
      dateTime: DateTime.now().subtract(const Duration(days: 1)),
      price: 25.0,
      currency: 'ريال',
      carType: 'اقتصادي',
      driverName: 'محمد علي',
      driverImage: 'assets/images/driver2.jpg',
      rating: 4.5,
    ),
    Trip(
      id: '3',
      from: 'الرياض - جامعة الملك سعود',
      to: 'الرياض - حي العليا',
      status: AppConstants.tripStatusCompleted,
      dateTime: DateTime.now().subtract(const Duration(days: 2)),
      price: 35.0,
      currency: 'ريال',
      carType: 'فاخر',
      driverName: 'سعد الأحمد',
      driverImage: 'assets/images/driver3.jpg',
      rating: 5.0,
    ),
  ];

  final List<Trip> cancelledTrips = [
    Trip(
      id: '4',
      from: 'الرياض - مستشفى الملك فيصل',
      to: 'الرياض - حي الورود',
      status: AppConstants.tripStatusCancelled,
      dateTime: DateTime.now().subtract(const Duration(days: 3)),
      price: 20.0,
      currency: 'ريال',
      carType: 'اقتصادي',
      driverName: 'خالد السعد',
      driverImage: 'assets/images/driver4.jpg',
      rating: 4.2,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'رحلاتي',
          style: TextStyle(
            color: AppColors.primaryText,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryGreen,
          unselectedLabelColor: AppColors.secondaryText,
          indicatorColor: AppColors.primaryGreen,
          tabs: const [
            Tab(text: 'الرحلات الحالية'),
            Tab(text: 'الرحلات المكتملة'),
            Tab(text: 'الرحلات الملغية'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTripsList(activeTrips, 'لا توجد رحلات حالية'),
          _buildTripsList(completedTrips, 'لا توجد رحلات مكتملة'),
          _buildTripsList(cancelledTrips, 'لا توجد رحلات ملغية'),
        ],
      ),
    );
  }

  Widget _buildTripsList(List<Trip> trips, String emptyMessage) {
    if (trips.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.directions_car_outlined,
              size: 64,
              color: AppColors.hintText,
            ),
            const SizedBox(height: AppConstants.mediumPadding),
            Text(
              emptyMessage,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.secondaryText,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.mediumPadding),
      itemCount: trips.length,
      itemBuilder: (context, index) {
        return TripCard(
          trip: trips[index],
          onTap: () {
            _showTripDetails(trips[index]);
          },
        );
      },
    );
  }

  void _showTripDetails(Trip trip) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildTripDetailsSheet(trip),
    );
  }

  Widget _buildTripDetailsSheet(Trip trip) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.borderRadius * 2),
          topRight: Radius.circular(AppConstants.borderRadius * 2),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppConstants.smallPadding),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.borderColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(AppConstants.mediumPadding),
            child: Row(
              children: [
                const Text(
                  'تفاصيل الرحلة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          // Trip details
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.mediumPadding,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Driver info
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppColors.primaryGreen,
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                      const SizedBox(width: AppConstants.mediumPadding),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              trip.driverName,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryText,
                              ),
                            ),
                            Row(
                              children: [
                                ...List.generate(5, (index) {
                                  return Icon(
                                    index < trip.rating.floor()
                                        ? Icons.star
                                        : Icons.star_border,
                                    color: Colors.amber,
                                    size: 18,
                                  );
                                }),
                                const SizedBox(width: 4),
                                Text(
                                  trip.rating.toString(),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: AppColors.secondaryText,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // Trip route
                  _buildRouteInfo(trip),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // Trip info
                  _buildTripInfo(trip),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // Action buttons
                  if (trip.status == AppConstants.tripStatusCompleted)
                    _buildActionButtons(trip),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRouteInfo(Trip trip) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: AppColors.primaryGreen,
                shape: BoxShape.circle,
              ),
            ),
            Container(
              width: 2,
              height: 40,
              color: AppColors.borderColor,
            ),
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: AppColors.errorColor,
                shape: BoxShape.circle,
              ),
            ),
          ],
        ),
        const SizedBox(width: AppConstants.mediumPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'من',
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.secondaryText,
                ),
              ),
              Text(
                trip.from,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.primaryText,
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'إلى',
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.secondaryText,
                ),
              ),
              Text(
                trip.to,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.primaryText,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTripInfo(Trip trip) {
    return Column(
      children: [
        _buildInfoRow('نوع السيارة', trip.carType),
        _buildInfoRow('التاريخ والوقت', _formatDateTime(trip.dateTime)),
        _buildInfoRow('المبلغ المدفوع', '${trip.price.toStringAsFixed(2)} ${trip.currency}'),
        _buildInfoRow('حالة الرحلة', _getStatusText(trip.status)),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.primaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Trip trip) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              // Handle repeat trip
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppColors.primaryGreen),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
            ),
            child: const Text(
              'إعادة الحجز',
              style: TextStyle(color: AppColors.primaryGreen),
            ),
          ),
        ),
        const SizedBox(width: AppConstants.mediumPadding),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              // Handle rate trip
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryGreen,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
            ),
            child: const Text(
              'تقييم الرحلة',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} - ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.tripStatusActive:
        return 'نشط';
      case AppConstants.tripStatusCompleted:
        return 'مكتمل';
      case AppConstants.tripStatusCancelled:
        return 'ملغي';
      default:
        return status;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
