import 'package:flutter/material.dart';
import '../models/car_type.dart';
import '../widgets/car_type_card.dart';
import '../utils/colors.dart';
import '../utils/constants.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int selectedCarTypeIndex = 0;
  final TextEditingController _searchController = TextEditingController();

  // Sample car types data
  final List<CarType> carTypes = [
    CarType(
      id: '1',
      name: 'اقتصادي',
      description: 'سيارة عادية • 4 مقاعد',
      pricePerKm: 2.5,
      currency: 'ريال',
      imageAsset: 'assets/images/economy_car.png',
      color: AppColors.economyColor,
      capacity: 4,
      estimatedTime: '5 دقائق',
    ),
    CarType(
      id: '2',
      name: 'مريح',
      description: 'سيارة مريحة • 4 مقاعد',
      pricePerKm: 3.5,
      currency: 'ريال',
      imageAsset: 'assets/images/comfort_car.png',
      color: AppColors.comfortColor,
      capacity: 4,
      estimatedTime: '3 دقائق',
    ),
    CarType(
      id: '3',
      name: 'فاخر',
      description: 'سيارة فاخرة • 4 مقاعد',
      pricePerKm: 5.0,
      currency: 'ريال',
      imageAsset: 'assets/images/luxury_car.png',
      color: AppColors.luxuryColor,
      capacity: 4,
      estimatedTime: '10 دقائق',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Map area (placeholder)
            Expanded(
              flex: 3,
              child: _buildMapArea(),
            ),
            
            // Car selection area
            Expanded(
              flex: 2,
              child: _buildCarSelectionArea(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.mediumPadding),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.person_outline,
                color: AppColors.primaryText,
                size: AppConstants.iconSize,
              ),
              const Spacer(),
              Text(
                AppConstants.appName,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryGreen,
                ),
              ),
              const Spacer(),
              const Icon(
                Icons.notifications_outlined,
                color: AppColors.primaryText,
                size: AppConstants.iconSize,
              ),
            ],
          ),
          const SizedBox(height: AppConstants.mediumPadding),
          
          // Search bar
          Container(
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: TextField(
              controller: _searchController,
              textAlign: TextAlign.right,
              decoration: const InputDecoration(
                hintText: 'أين وجهتك؟',
                hintStyle: TextStyle(color: AppColors.hintText),
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.hintText,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: AppConstants.mediumPadding,
                  vertical: AppConstants.mediumPadding,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapArea() {
    return Container(
      width: double.infinity,
      color: AppColors.backgroundColor,
      child: Stack(
        children: [
          // Map placeholder
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[300],
              image: const DecorationImage(
                image: NetworkImage(
                  'https://via.placeholder.com/400x300/E0E0E0/757575?text=Map+Area',
                ),
                fit: BoxFit.cover,
              ),
            ),
          ),
          
          // Current location button
          Positioned(
            bottom: AppConstants.mediumPadding,
            right: AppConstants.mediumPadding,
            child: FloatingActionButton(
              mini: true,
              backgroundColor: Colors.white,
              onPressed: () {
                // Handle current location
              },
              child: const Icon(
                Icons.my_location,
                color: AppColors.primaryGreen,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCarSelectionArea() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppConstants.borderRadius * 2),
          topRight: Radius.circular(AppConstants.borderRadius * 2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppConstants.mediumPadding),
          
          // Section title
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: AppConstants.mediumPadding),
            child: Text(
              'اختر نوع السيارة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.smallPadding),
          
          // Car types list
          Expanded(
            child: ListView.builder(
              itemCount: carTypes.length,
              itemBuilder: (context, index) {
                return CarTypeCard(
                  carType: carTypes[index],
                  isSelected: selectedCarTypeIndex == index,
                  onTap: () {
                    setState(() {
                      selectedCarTypeIndex = index;
                    });
                  },
                );
              },
            ),
          ),
          
          // Book button
          Padding(
            padding: const EdgeInsets.all(AppConstants.mediumPadding),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () {
                  // Handle booking
                  _showBookingDialog();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryGreen,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                ),
                child: const Text(
                  'احجز الآن',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showBookingDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحجز'),
          content: Text(
            'هل تريد حجز ${carTypes[selectedCarTypeIndex].name}؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Handle booking confirmation
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم الحجز بنجاح!'),
                    backgroundColor: AppColors.successColor,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
              ),
              child: const Text('تأكيد', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
