class Offer {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final double discount;
  final String discountType; // percentage or fixed
  final DateTime validUntil;
  final bool isActive;
  final String? promoCode;

  Offer({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.discount,
    required this.discountType,
    required this.validUntil,
    required this.isActive,
    this.promoCode,
  });

  factory Offer.fromJson(Map<String, dynamic> json) {
    return Offer(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      discount: json['discount'].toDouble(),
      discountType: json['discountType'],
      validUntil: DateTime.parse(json['validUntil']),
      isActive: json['isActive'],
      promoCode: json['promoCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'discount': discount,
      'discountType': discountType,
      'validUntil': validUntil.toIso8601String(),
      'isActive': isActive,
      'promoCode': promoCode,
    };
  }
}
